# Vector store model implementation
import chromadb
from langchain_chroma import Chroma
from langchain_huggingface import HuggingFaceEmbeddings
from config import Config

class VectorStore:
    def __init__(self, path):
        self.embeddings = HuggingFaceEmbeddings(model_name=Config.HF_EMBEDDING_MODEL)
        self.vector_store = Chroma(
            persist_directory=path,
            embedding_function=self.embeddings
        )
    
    def add_document(self, documents):
        self.vector_store.add_documents(documents)
    
    def similarity_search(self, query, k=4):
        return self.vector_store.similarity_search(query, k=k)
 
        