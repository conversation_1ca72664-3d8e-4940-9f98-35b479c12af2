Metadata-Version: 2.1
Name: langchain-chroma
Version: 0.2.5
Summary: An integration package connecting Chroma and LangChain.
License: MIT
Project-URL: Source Code, https://github.com/langchain-ai/langchain/tree/master/libs/partners/chroma
Project-URL: Release Notes, https://github.com/langchain-ai/langchain/releases?q=tag%3A%22langchain-chroma%3D%3D0%22&expanded=true
Project-URL: repository, https://github.com/langchain-ai/langchain
Requires-Python: >=3.9
Requires-Dist: langchain-core>=0.3.70
Requires-Dist: numpy>=1.26.0; python_version < "3.13"
Requires-Dist: numpy>=2.1.0; python_version >= "3.13"
Requires-Dist: chromadb>=1.0.9
Description-Content-Type: text/markdown

# langchain-chroma

This package contains the Lang<PERSON>hain integration with Chroma.

## Installation

```bash
pip install -U langchain-chroma
```

## Usage

The `Chroma` class exposes the connection to the Chroma vector store.

```python
from langchain_chroma import Chroma

embeddings = ... # use a LangChain Embeddings class

vectorstore = Chroma(embeddings=embeddings)
```
