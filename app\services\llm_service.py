# LLM service implementation
import logging
from langchain_groq import <PERSON><PERSON>Groq
from langchain.chains import ConversationalR<PERSON>rie<PERSON><PERSON>hain
from langchain.memory import ConversationBufferMemory
from config import Config

logger = logging.getLogger(__name__)

class LLMService:
    def __init__(self, vector_store):
        try:
            if not Config.GROQ_API_KEY:
                raise ValueError("GROQ_API_KEY not found in environment variables")
            
            self.llm = ChatGroq(
                groq_api_key=Config.GROQ_API_KEY,
                model_name="llama-3-70b-8192",  # Using Llama-3 70B model (versatile)
                temperature=0.7,
                max_tokens=1024
            )
            
            self.memory = ConversationBufferMemory(
                memory_key="chat_history",
                return_messages=True
            )
            
            self.chain = ConversationalRetrievalChain.from_llm(
                llm=self.llm,
                retriever=vector_store.vector_store.as_retriever(
                    search_kwargs={"k": 3}  # Return top 3 most relevant chunks
                ),
                memory=self.memory,
                verbose=True  # For debugging
            )
            logger.info("LLMService initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing LLMService: {str(e)}")
            raise
    
    def get_response(self, query):
        try:
            if not query or not isinstance(query, str):
                raise ValueError("Invalid query format")
            
            logger.info(f"Processing query: {query}")
            response = self.chain({"question": query})
            
            if not response or 'answer' not in response:
                raise ValueError("Invalid response format from LLM")
                
            logger.info("Successfully generated response")
            return response['answer']
            
        except Exception as e:
            logger.error(f"Error getting LLM response: {str(e)}")
            raise  # Re-raise the exception for the main app to handle
        
    