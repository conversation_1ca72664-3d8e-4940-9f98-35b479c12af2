<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced RAG App</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <h1>Advanced RAG Document QA</h1>
        <section class="upload-section">
            <h2>Upload Document</h2>
            <form id="uploadForm">
                <input type="file" id="fileInput" name="file" accept=".pdf,.txt" required>
                <button type="submit">Upload</button>
            </form>
            <div id="uploadResult" class="result"></div>
        </section>
        <section class="query-section">
            <h2>Ask a Question</h2>
            <form id="queryForm">
                <input type="text" id="questionInput" name="question" placeholder="Type your question..." required>
                <button type="submit">Ask</button>
            </form>
            <div id="queryResult" class="result"></div>
        </section>
    </div>
    <script>
        // Handle document upload
        document.getElementById('uploadForm').onsubmit = async function(e) {
            e.preventDefault();
            const fileInput = document.getElementById('fileInput');
            const resultDiv = document.getElementById('uploadResult');
            resultDiv.textContent = 'Uploading...';
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            try {
                const res = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });
                const data = await res.json();
                if (res.ok) {
                    if (data.chunks && Array.isArray(data.chunks)) {
                        resultDiv.textContent = `Chunks processed: ${data.chunks.length}`;
                    } else if (data.message) {
                        resultDiv.textContent = data.message;
                    } else {
                        resultDiv.textContent = 'Upload successful.';
                    }
                } else {
                    resultDiv.textContent = data.error || 'Upload failed.';
                }
            } catch (err) {
                resultDiv.textContent = 'Error uploading file.';
            }
        };
        // Handle question query
        document.getElementById('queryForm').onsubmit = async function(e) {
            e.preventDefault();
            const questionInput = document.getElementById('questionInput');
            const resultDiv = document.getElementById('queryResult');
            resultDiv.textContent = 'Thinking...';
            try {
                const res = await fetch('/query', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ question: questionInput.value })
                });
                const data = await res.json();
                if (res.ok) {
                    resultDiv.textContent = data.response || 'No answer.';
                } else {
                    resultDiv.textContent = data.error || 'Query failed.';
                }
            } catch (err) {
                resultDiv.textContent = 'Error querying.';
            }
        };
    </script>
</body>
</html>