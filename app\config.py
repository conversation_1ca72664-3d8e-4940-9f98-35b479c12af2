# Configuration settings for the app
import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Set the GROQ API key from the environment variable
    GROQ_API_KEY = os.getenv("GROQ_API_KEY")
    GROQ_MODEL = "qwen:3-32b"
    # Set the best HuggingFace embedding model
    HF_EMBEDDING_MODEL = "sentence-transformers/all-MiniLM-L6-v2"
    # Set the AWS credentials from the environment variables
    AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
    AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
    AWS_REGION = "us-east-1"  # Fixed region format
    AWS_BUCKET_NAME = os.getenv("AWS_BUCKET_NAME")
    # Set the vector database path
    Vector_DB_PATH = "vector_db"
    