Advanced RAG System Test Document

This is a test document for the Advanced RAG (Retrieval-Augmented Generation) system.

The system includes the following components:
1. Document Upload: Users can upload PDF and TXT files
2. Vector Store: Documents are processed and stored in ChromaDB
3. LLM Integration: Uses GROQ's Llama-3 model for question answering
4. AWS S3 Storage: Files are stored in AWS S3 for persistence

Key Features:
- Document chunking with RecursiveCharacterTextSplitter
- Semantic search using HuggingFace embeddings
- Conversational memory for context retention
- Web interface for easy interaction

This document can be used to test the upload and query functionality of the RAG system.
