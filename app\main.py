# Entry point for the application
from flask import Flask, request, render_template, jsonify
from app.models.vector_store import VectorStore
from app.services.storage_service import S3Storage
from app.services.llm_service import LLMService
from app.config import Config
import os
from langchain_community.document_loaders import <PERSON>y<PERSON><PERSON>oa<PERSON>, TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
import tempfile
import logging

app = Flask(__name__)
vector_store = VectorStore(Config.Vector_DB_PATH) # Objects created of 3 services
storage_service = S3Storage()
llm_service = LLMService(vector_store)


@app.route('/')   # setting the default route to the index.html template
def index():
    return render_template('index.html')


#Configure logging

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


def process_document(file):
    """Process document based on file type and return text chunks"""
    temp_dir = tempfile.mkdtemp()
    temp_path = os.path.join(temp_dir, file.filename)
    
    try:
        # Save this file temporarily
        file.save(temp_path)
        
        # Process file based on file type
        if file.filename.endswith('.pdf'):
            loader = PyPDFLoader(temp_path)
            documents = loader.load()
        elif file.filename.endswith('.txt'):
            loader = TextLoader(temp_path)
            documents = loader.load()
        else:
            raise ValueError(f"Unsupported file type: {file.filename}")
        
        # Split documents into chunks
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
        text_chunks = text_splitter.split_documents(documents)
        
        return text_chunks
    finally:
        # Clean up Temp File
        if os.path.exists(temp_path):
            os.remove(temp_path)
        os.rmdir(temp_dir)
        
@app.route('/upload', methods=['POST'])
def upload_document():
    try:
        logger.debug("Upload endpoint called")
        
        if 'file' not in request.files:
            logger.warning("No such file in request")
            return jsonify({"error": "No file provided"}), 400
        
        file = request.files['file']
        if file.filename == '':
            logger.warning("Empty Filename")
            return jsonify({"error": "No file provided"}), 400    

        # Check file extension 
        if not file.filename.endswith(('.pdf', '.txt')):
            logger.warning(f"Unsupported file type: {file.filename}")
            return jsonify({"error": "Only pdf and txt files are supported"}), 415
        
        logger.debug(f"Processing file: {file.filename}")
        
        #Process the document
        try:
            text_chunks = process_document(file)
            logger.debug(f"Document processed into{len(text_chunks)} chunks")
        except Exception as e:
            logger.error(f"Error processing document: {str(e)}")
            return jsonify({"error": f"Error processing document : {str(e)}"}), 500
        
        # Add to Vector Store
        try:
            vector_store.add_documents(text_chunks)
            logger.debug("Documents added to vector store")
        except Exception as e:
            logger.error(f"Error adding documents to vector store: {str(e)}")
            return jsonify({"error": f"Error adding documents to vector store : {str(e)}"}), 500

        #Upload to S3
        try:
            file.seek(0)  # reset the file pointer
            storage_service.upload_file(file,file.filename)
            logger.debug("File uploaded to S3")
        except Exception as e:
            logger.error(f"Error uploading file to S3: {str(e)}")
            return jsonify({"error": f"Error uploading file to S3 : {str(e)}"}), 500

        return jsonify({"message": "File uploaded and processed successfully",
                        "chunks_processed": len(text_chunks)})
        
    except Exception as e:
        logger.error(f"Unexpected error in upload endpoint: {str(e)}")
        return jsonify({"error": f"Error in upload endpoint : {str(e)}"}), 500
                
    
@app.route('/query', methods=['POST'])
def query():
    try:
        data = request.get_json()
        if not data:
            logger.warning("No JSON data provided in request")
            return jsonify({"error": "No JSON data provided"}), 400
        if 'question' not in data:
            logger.warning("No question field in request data")
            return jsonify({"error": "No question provided"}), 400
        
        logger.info(f"Processing query: {data['question']}")
        
        try:
            response = llm_service.get_response(data['question'])
            if not response:
                logger.error("LLM returned empty response")
                return jsonify({"error": "No response generated"}), 500
                
            logger.info("Successfully generated response")
            return jsonify({"response": response})
            
        except Exception as e:
            logger.error(f"LLM Service error: {str(e)}")
            return jsonify({
                "error": "Error processing your request",
                "details": str(e) if app.debug else "Please try again later"
            }), 500
            
    except Exception as e:
        logger.error(f"Query endpoint error: {str(e)}")
        return jsonify({
            "error": "Server error",
            "details": str(e) if app.debug else "An unexpected error occurred"
        }), 500
    

if __name__ == '__main__':
    app.run(debug=True, host='127.0.0.1', port=5000)
    
# Entry point for the application
    